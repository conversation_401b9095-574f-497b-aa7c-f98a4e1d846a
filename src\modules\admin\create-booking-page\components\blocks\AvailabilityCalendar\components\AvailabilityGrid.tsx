import type { AvailabilityGridProps } from '../types'
import { cn } from '@/libs/utils'
import { useBookingSlotsStore } from '@/modules/booking/stores/booking-slots.store'
import { CheckCircle, Clock, MapPin, User } from 'lucide-react'
import React from 'react'
import { useIsSlotSelected, useToggleBookingSlot } from '../store/availabilityCalendarStore'

/**
 * Availability grid component for displaying time slots and fields
 */
export const AvailabilityGridBase: React.FC<AvailabilityGridProps> = ({
  timeSlots,
  displayFields,
  selectedDate,
}) => {
  // Get helpers from the stores
  const isSlotSelected = useIsSlotSelected()
  const toggleBookingSlot = useToggleBookingSlot()
  const { getSlotStatus } = useBookingSlotsStore()

  // Create a memoized toggle handler that includes the selected date
  const handleToggleBookingSlot = (fieldId: string, time: string) => {
    // Only allow selection if slot is available
    const slotStatus = getSlotStatus(fieldId, time)

    if (slotStatus === 'available') {
      toggleBookingSlot(fieldId, time, selectedDate)
    }
  }

  // Helper function to get status styling and icon
  const getSlotStyling = (slotStatus: string, isSelected: boolean) => {
    if (isSelected) {
      return {
        containerClass: 'bg-blue-100 border-blue-300 ring-2 ring-blue-200 hover:bg-blue-200',
        textClass: 'text-blue-700',
        icon: <User className="h-4 w-4" />,
        statusText: 'Đã chọn',
        cursor: 'cursor-pointer',
      }
    }

    switch (slotStatus) {
      case 'available':
        return {
          containerClass: 'bg-green-50 border-green-200 hover:bg-green-100 hover:border-green-300',
          textClass: 'text-green-700',
          icon: <CheckCircle className="h-4 w-4" />,
          statusText: 'Còn trống',
          cursor: 'cursor-pointer',
        }
      case 'pending':
        return {
          containerClass: 'bg-yellow-50 border-yellow-200',
          textClass: 'text-yellow-700',
          icon: <Clock className="h-4 w-4" />,
          statusText: 'Chờ xác nhận',
          cursor: 'cursor-not-allowed',
        }
      case 'confirmed':
        return {
          containerClass: 'bg-red-50 border-red-200',
          textClass: 'text-red-700',
          icon: <CheckCircle className="h-4 w-4 fill-current" />,
          statusText: 'Đã đặt',
          cursor: 'cursor-not-allowed',
        }
      default:
        return {
          containerClass: 'bg-gray-50 border-gray-200',
          textClass: 'text-gray-500',
          icon: <MapPin className="h-4 w-4" />,
          statusText: 'Không khả dụng',
          cursor: 'cursor-not-allowed',
        }
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-x-auto">
      <div className="min-w-[600px]">
        {/* Header with field names */}
        <div className="grid border-b" style={{ gridTemplateColumns: `repeat(${displayFields.length}, 1fr)` }}>
          {displayFields.map(field => (
            <div
              key={field.id}
              className="p-2 text-center font-medium border-r last:border-r-0 bg-gray-50"
            >
              <div>{field.name}</div>
            </div>
          ))}
        </div>

        {/* Time slots */}
        <div>
          {timeSlots.map(slot => (
            <div
              key={`slot-${slot.time}`}
              className="grid border-b"
              style={{ gridTemplateColumns: `repeat(${displayFields.length}, 1fr)` }}
            >
              {/* Availability cells for each field */}
              {displayFields.map((field) => {
                const slotStatus = getSlotStatus(field.id, slot.time)
                const isAvailable = slotStatus === 'available'
                const isSelected = isSlotSelected(field.id, slot.time)
                const styling = getSlotStyling(slotStatus, isSelected)

                return (
                  <div
                    key={`${field.id}-${slot.time}`}
                    role="button"
                    tabIndex={0}
                    className={cn(
                      'p-3 h-24 border-r flex items-center justify-center transition-all duration-200 border-2',
                      styling.containerClass,
                      styling.cursor,
                    )}
                    onClick={() => {
                      if (isAvailable) {
                        handleToggleBookingSlot(field.id, slot.time)
                      }
                    }}
                  >
                    <div className="flex flex-col items-center justify-center w-full space-y-1">
                      {/* Time slot indicator */}
                      <div className={cn(
                        'text-xs font-semibold rounded-full px-2 py-1',
                        isSelected ? 'bg-blue-200 text-blue-800' : 'bg-white/80 text-gray-700',
                      )}
                      >
                        {slot.time}
                      </div>

                      {/* Status indicator with icon and text */}
                      <div className={cn(
                        'flex items-center gap-1 text-xs font-medium',
                        styling.textClass,
                      )}
                      >
                        {styling.icon}
                        <span>{styling.statusText}</span>
                      </div>

                      {/* Field location indicator */}
                      <div className="flex items-center gap-1 text-xs text-gray-600">
                        <MapPin className="h-3 w-3" />
                        <span className="truncate max-w-[60px]">{field.name}</span>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export const AvailabilityGrid = React.memo(AvailabilityGridBase)
